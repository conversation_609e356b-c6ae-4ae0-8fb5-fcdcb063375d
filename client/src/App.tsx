import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { LangChainProvider } from "@/contexts/LangChainContext";
import { useAuth } from "@/hooks/useAuth";
import NotFound from "@/pages/not-found";
import Auth from "@/pages/auth";
import Home from "@/pages/home";
import Settings from "@/pages/settings";
import Psychology from "@/pages/psychology";

function Router() {
  const { isAuthenticated, isLoading } = useAuth();

  return (
    <Switch>
      {isLoading || !isAuthenticated ? (
        <Route path="/" component={Auth} />
      ) : (
        <>
          <Route path="/" component={Home} />
          <Route path="/chat/:chatId" component={Home} />
          <Route path="/psychology" component={Home} />
          <Route path="/psychology/:chatId" component={Home} />
          <Route path="/settings" component={Settings} />
        </>
      )}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <LangChainProvider>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </LangChainProvider>
    </QueryClientProvider>
  );
}

export default App;
